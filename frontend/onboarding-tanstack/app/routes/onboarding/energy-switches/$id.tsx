import { createFileRoute, redirect } from '@tanstack/react-router'
import { useEnergySwitch } from '@/hooks/useEnergySwitchData'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { ErrorMessage } from '@/components/ui/error-message'
import { EnergyResults } from '@/components/onboarding/energy-results'

export const Route = createFileRoute('/onboarding/energy-switches/$id')({
  beforeLoad: ({ context, params }) => {
    // Check if user is authenticated
    if (!context.auth.isAuthenticated) {
      throw redirect({
        to: '/onboarding',
        search: {
          redirect: `/onboarding/energy-switches/${params.id}`,
        },
      })
    }

    // Check if user has access to this specific energy switch
    const user = context.auth.user
    if (user && user.energySwitchId !== params.id) {
      console.warn('User trying to access different energy switch:', {
        userSwitchId: user.energySwitchId,
        requestedId: params.id,
      })
      throw redirect({
        to: `/onboarding/energy-switches/${user.energySwitchId}`,
      })
    }
  },
  component: EnergySwitchPage,
})

function EnergySwitchPage() {
  const { id } = Route.useParams()
  
  const { data: energySwitch, isLoading, error } = useEnergySwitch(id)

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="text-center">
          <LoadingSpinner size="large" />
          <h2 className="mt-4 text-xl font-semibold text-gray-700">Loading your energy switch...</h2>
          <p className="mt-2 text-gray-500">Please wait while we fetch your information.</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <ErrorMessage 
        title="Error Loading Energy Switch" 
        message="We couldn't load your energy switch information. Please try again later."
        actionText="Try Again"
        actionHref={`/onboarding/energy-switches/${id}`}
      />
    )
  }

  if (!energySwitch) {
    return (
      <ErrorMessage 
        title="Energy Switch Not Found" 
        message="We couldn't find the energy switch you're looking for."
        actionText="Return to Dashboard"
        actionHref="/onboarding"
      />
    )
  }

  return <EnergyResults energySwitch={energySwitch} />
}