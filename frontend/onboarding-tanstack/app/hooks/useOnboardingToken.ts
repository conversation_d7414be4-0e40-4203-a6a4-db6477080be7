import { useState, useEffect } from 'react';

/**
 * Custom hook to manage onboarding token from sessionStorage
 * This hook handles the token lifecycle and cleanup
 */
export function useOnboardingToken() {
  const [token, setToken] = useState<string | null>(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') {
      setIsReady(true);
      return;
    }

    // Get token from sessionStorage (set by OnboardingLinkHandler)
    const storedToken = sessionStorage.getItem('onboardingToken');
    setToken(storedToken);
    setIsReady(true);

    // Log for debugging
    console.log('useOnboardingToken: Retrieved token from sessionStorage:', storedToken ? 'Token present' : 'No token');
  }, []);

  /**
   * Store the verified token as authToken for API calls and clear onboarding token
   */
  const completeVerification = (verifiedToken: string) => {
    if (typeof window === 'undefined') return;
    
    // Store verified token for API calls
    sessionStorage.setItem('authToken', verifiedToken);
    
    // Clear the onboarding token as it's no longer needed
    sessionStorage.removeItem('onboardingToken');
    
    console.log('useOnboardingToken: Token verification completed, authToken stored');
  };

  return {
    token,
    isReady,
    completeVerification,
  };
}