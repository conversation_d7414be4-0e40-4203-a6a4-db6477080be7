import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { EnergySwitch, SwitchStatusType } from '@/types/types';

/**
 * Base fetcher function that handles authentication
 */
const apiFetcher = (token: string) => async (url: string) => {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  });

  if (!response.ok) {
    const error = new Error('An error occurred while fetching the data.');
    error.message = `${response.status}: ${response.statusText}`;
    throw error;
  }

  return response.json();
};

/**
 * Helper function to get access token from session/storage
 * This could be enhanced to use the auth context, but keeping simple for now
 */
export function getAccessToken(): string | null {
  if (typeof window === 'undefined') return null;
  
  // Get token from sessionStorage (from the onboarding flow)
  // In production, this would integrate with your authentication system
  return sessionStorage.getItem('authToken') || null;
}

/**
 * Hook to fetch energy switch data with TanStack Query
 */
export function useEnergySwitch(switchId: string | null) {
  const token = getAccessToken();
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || '';
  
  return useQuery({
    queryKey: ['energySwitch', switchId],
    queryFn: () => apiFetcher(token!)(`${baseUrl}/api/v1/energy_switches/${switchId}/tariff_comparison`),
    enabled: !!switchId && !!token,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch energy switch status with TanStack Query
 */
export function useEnergySwitchStatus(switchId: string | null) {
  const token = getAccessToken();
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || '';
  
  return useQuery({
    queryKey: ['energySwitchStatus', switchId],
    queryFn: () => apiFetcher(token!)(`${baseUrl}/api/v1/energy_switches/${switchId}/switch_status`),
    enabled: !!switchId && !!token,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 30 * 1000, // Refetch every 30 seconds for status updates
  });
}

/**
 * Hook for confirming an energy switch using TanStack Query
 */
export function useConfirmEnergySwitch(switchId: string | null) {
  const queryClient = useQueryClient();
  const token = getAccessToken();
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || '';
  
  return useMutation({
    mutationFn: async ({ switchTo, formData }: { switchTo: string; formData: any }) => {
      const url = `${baseUrl}/api/v1/energy_switches/${switchId}/confirm_switch`;
      
      const requestBody = {
        ...formData,
        switch_to: switchTo
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error('Failed to confirm switch');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch switch status after successful confirmation
      queryClient.invalidateQueries({ queryKey: ['energySwitchStatus', switchId] });
      queryClient.invalidateQueries({ queryKey: ['energySwitch', switchId] });
    }
  });
}

/**
 * Local storage functions for switch ID
 * 
 * Note: These functions are deprecated and should not be used.
 * The energy switch ID should be obtained from the authenticated session
 * via session.user.energySwitchId
 */
export function storeVerifiedSwitchId(id: string): void {
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('verifiedSwitchId', id);
  }
}

export function getVerifiedSwitchId(): string | null {
  if (typeof window !== 'undefined') {
    return sessionStorage.getItem('verifiedSwitchId');
  }
  return null;
}