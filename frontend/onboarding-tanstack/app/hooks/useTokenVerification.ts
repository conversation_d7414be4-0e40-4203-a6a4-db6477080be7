import { useQuery } from '@tanstack/react-query';

interface TokenVerificationResponse {
  success: boolean;
  energy_switch_id: string;
  user_id: string;
  status: string;
  has_gas: boolean;
  has_electricity: boolean;
}

interface User {
  id: string;
  energySwitchId: string;
  hasGas: boolean;
  hasElectricity: boolean;
  status: string;
}

/**
 * Custom hook to verify onboarding token using TanStack Query
 */
export function useTokenVerification(token: string | null) {
  return useQuery({
    queryKey: ['token-verification', token],
    queryFn: async (): Promise<User> => {
      if (!token) {
        throw new Error('No token provided');
      }
      
      const baseUrl = process.env.API_URL || '';
      const response = await fetch(`${baseUrl}/api/v1/onboarding/verify`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Token verification failed: ${response.status} ${errorText}`);
      }
      
      const result: TokenVerificationResponse = await response.json();
      
      // Transform backend response to user format
      return {
        id: result.user_id,
        energySwitchId: result.energy_switch_id,
        hasGas: result.has_gas,
        hasElectricity: result.has_electricity,
        status: result.status,
      };
    },
    enabled: !!token,
    retry: (failureCount, error) => {
      // Only retry on network errors, not auth errors
      return failureCount < 2 && !error.message.includes('401') && !error.message.includes('403');
    },
    staleTime: Infinity, // Token verification result never goes stale
    gcTime: 0, // Don't cache after component unmounts
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
}