import { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface User {
  id: string;
  energySwitchId: string;
  hasGas: boolean;
  hasElectricity: boolean;
  status: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  setUser: (user: User | null) => void;
  login: (user: User, token: string) => void;
  logout: () => void;
  getAuthToken: () => string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Authentication context provider for managing global auth state
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);

  const login = useCallback((userData: User, token: string) => {
    setUser(userData);
    
    // Store auth token for API calls
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('authToken', token);
    }
    
    console.log('AuthContext: User logged in', userData);
  }, []);

  const logout = useCallback(() => {
    setUser(null);
    
    // Clear all auth-related tokens
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem('authToken');
      sessionStorage.removeItem('onboardingToken');
    }
    
    console.log('AuthContext: User logged out');
  }, []);

  const getAuthToken = useCallback((): string | null => {
    if (typeof window === 'undefined') return null;
    return sessionStorage.getItem('authToken');
  }, []);

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    setUser,
    login,
    logout,
    getAuthToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

/**
 * Hook to use the authentication context
 */
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}