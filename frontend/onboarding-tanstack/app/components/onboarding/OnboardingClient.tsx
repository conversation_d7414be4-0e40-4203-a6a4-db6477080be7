import { useEffect } from 'react';
import { useRouter } from '@tanstack/react-router';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { ErrorMessage } from '@/components/ui/error-message';
import { useOnboardingToken } from '@/hooks/useOnboardingToken';
import { useTokenVerification } from '@/hooks/useTokenVerification';
import { useAuth } from '@/context/AuthContext';

export function OnboardingClient() {
  const router = useRouter();
  const { user, login } = useAuth();
  const { token, isReady, completeVerification } = useOnboardingToken();
  const { data: verifiedUser, isLoading, error, isSuccess } = useTokenVerification(token);

  console.log('OnboardingClient:', { 
    hasToken: !!token, 
    isReady, 
    isLoading, 
    hasUser: !!user,
    hasVerifiedUser: !!verifiedUser 
  });

  // Handle successful token verification
  useEffect(() => {
    if (isSuccess && verifiedUser && token) {
      console.log('✅ Token verification successful, logging in user');
      
      // Complete the verification process (store authToken, clear onboardingToken)
      completeVerification(token);
      
      // Login user with verified data
      login(verifiedUser, token);
    }
  }, [isSuccess, verifiedUser, token, login, completeVerification]);

  // Redirect to energy switch page once authenticated
  useEffect(() => {
    if (user?.energySwitchId) {
      console.log('✅ Authentication complete! Redirecting to energy switch:', user.energySwitchId);
      router.navigate({ to: `/onboarding/energy-switches/${user.energySwitchId}` });
    }
  }, [user, router]);

  // Handle no token case
  if (isReady && !token) {
    return (
      <ErrorMessage 
        title="Verification Failed" 
        message="No token provided. Please use the link from your email."
        actionText="Return to Website"
        actionHref="https://meetgeorge.co.uk/"
      />
    );
  }

  // Handle error state from token verification
  if (error) {
    return (
      <ErrorMessage 
        title="Verification Failed" 
        message={
          error.message || 
          'We were unable to verify your onboarding link. Please check that you\'re using the complete link from your most recent email. If you continue to experience issues, please contact our support <NAME_EMAIL> or try requesting a new link.'
        }
        actionText="Return to Website"
        actionHref="https://meetgeorge.co.uk/"
      />
    );
  }
  
  // Loading state - waiting for token or verifying
  if (!isReady || isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="text-center">
          <LoadingSpinner size="large" />
          <h2 className="mt-4 text-xl font-semibold text-gray-700">Verifying your link...</h2>
          <p className="mt-2 text-gray-500">Please wait while we confirm your details.</p>
        </div>
      </div>
    );
  }
  
  // Authenticated and verified, waiting for redirect
  if (user?.energySwitchId) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="text-center">
          <LoadingSpinner size="small" />
          <h2 className="text-xl font-semibold text-gray-700">Redirecting...</h2>
          <p className="mt-2 text-gray-500">You are being redirected to your energy switch details.</p>
        </div>
      </div>
    );
  }
  
  // Fallback state
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="text-center">
        <LoadingSpinner size="large" />
        <h2 className="text-xl font-semibold text-gray-700">Loading your information...</h2>
        <p className="mt-2 text-gray-500">Please wait while we prepare your page.</p>
      </div>
    </div>
  );
}