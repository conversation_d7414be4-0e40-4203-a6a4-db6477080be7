{"name": "@tanstack/router-devtools-core", "version": "1.128.6", "description": "Modern and scalable routing for Web applications", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/router.git", "directory": "packages/router-devtools-core"}, "homepage": "https://tanstack.com/router", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["react", "location", "router", "routing", "async", "async router", "typescript"], "type": "module", "types": "./dist/esm/index.d.ts", "main": "./dist/cjs/index.cjs", "module": "./dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "src"], "engines": {"node": ">=12"}, "dependencies": {"clsx": "^2.1.1", "goober": "^2.1.16", "solid-js": "^1.9.5"}, "devDependencies": {"vite-plugin-solid": "^2.11.6"}, "peerDependencies": {"tiny-invariant": "^1.3.3", "csstype": "^3.0.10", "solid-js": ">=1.9.5", "@tanstack/router-core": "^1.128.6"}, "peerDependenciesMeta": {"csstype": {"optional": true}}, "scripts": {}}